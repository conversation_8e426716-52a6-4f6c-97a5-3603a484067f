# SynchronizeData 项目文档

## 项目概述

本项目是一个基于 Spring Boot 的数据同步服务，主要用于从多个数据源（如逆变器、电表、传感器等）采集数据，并将这些数据同步到云端数据库中。项目通过定时任务调度实现数据的定期采集和同步，支持多数据源动态切换、日志记录、异常处理等功能。

---

## 技术栈

- **核心框架**: Spring Boot 2.3.5.RELEASE
- **持久层框架**: MyBatis-Plus 3.5.2
- **多数据源支持**: dynamic-datasource-spring-boot-starter 2.5.7
- **日志框架**: Logback
- **工具库**: Hutool 5.8.21
- **数据库**: MySQL 8.0.32
- **API 文档**: Springdoc OpenAPI 1.6.8
- **构建工具**: Maven

---

## 功能模块

### 1. 数据采集与同步

项目通过定时任务调度实现以下数据的采集与同步：

- **逆变器数据**:
  - 每 10 分钟采集一次逆变器实时数据。
  - 数据包括功率、发电量、信号强度等。
  - 实现类: `BtoInverterServiceImpl`

- **并网柜数据**:
  - 每 30 分钟采集一次并网柜电压电流数据。
  - 数据包括电压 A/B/C、电流 A/B/C 等。
  - 实现类: `BtoCounterServiceImpl`

- **多功能仪表数据**:
  - 每 30 分钟采集一次多功能仪表数据。
  - 数据包括正向有功电能、反向有功电能等。
  - 实现类: `BtoFunctionalInstrumentServiceImpl`

- **红外抄表数据**:
  - 每 10 分钟采集一次供电电表数据。
  - 数据包括正向有功总电能、反向有功总电能等。
  - 实现类: `BtoInfraredMeterServiceImpl`

- **传感器数据**:
  - 每 5 分钟采集一次传感器数据。
  - 数据包括烟雾浓度、温度、湿度等。
  - 实现类: `BtoSensorDataServiceImpl`

- **消防联动数据**:
  - 每 5 分钟采集一次消防联动数据。
  - 数据包括报警状态、时间等。
  - 实现类: `BtoFireFightingServiceImpl`

### 2. 多数据源支持

项目使用 `dynamic-datasource-spring-boot-starter` 实现多数据源动态切换，主要数据源包括：

- **本地数据源**: 用于存储采集到的原始数据。
- **云端数据源**: 用于存储同步后的数据。

数据源配置位于 `application.yml` 文件中。

### 3. 定时任务调度

项目通过 Spring 的 `@Scheduled` 注解实现定时任务调度，主要包括以下任务：

- `executeOne`: 每 5 分钟执行一次，采集传感器数据和消防联动数据。
- `executeTwo`: 每 30 分钟执行一次，采集并网柜数据和多功能仪表数据。
- `executeThree`: 每 10 分钟执行一次，采集逆变器数据和红外抄表数据。

定时任务实现类: `SynchronizationTask`

### 4. 日志记录

项目使用 Logback 进行日志记录，日志配置位于 `logback-spring.xml` 文件中。日志分为以下级别：

- **INFO**: 记录正常运行信息。
- **ERROR**: 记录错误信息。
- **DEBUG**: 记录调试信息。

日志文件按日期滚动存储，路径为 `logs/synchronize-data/`。

---

## 配置说明

### 1. 数据源配置

在 `application.yml` 文件中配置了两个数据源：

### 2. 定时任务配置

定时任务的调度间隔可以通过修改 `@Scheduled` 注解中的 `fixedDelay` 参数进行调整。例如：


### 3. 日志配置

日志配置位于 `logback-spring.xml` 文件中，支持按日期滚动存储日志文件。例如：

---

## 如何运行项目

1. **环境准备**:
   - JDK 1.8+
   - MySQL 8.0+
   - Maven 3.6+

2. **配置数据源**:
   - 修改 `application.yml` 文件中的数据源配置，确保连接到正确的数据库。

3. **构建项目**:

    - mvn clean install
4. **运行项目**:
5. **访问 API**:
   - 默认端口为 `8228`，可以通过浏览器或 Postman 访问 `/test` 接口测试服务是否正常运行。

---

## 扩展与维护

### 1. 添加新数据源

如果需要添加新的数据源，可以在 `application.yml` 文件中新增数据源配置，并在服务层使用 `@DS` 注解指定数据源。例如：


### 2. 新增定时任务

如果需要新增定时任务，可以在 `SynchronizationTask` 类中新增方法，并使用 `@Scheduled` 注解定义调度规则。

### 3. 调整日志级别

如果需要调整日志级别，可以修改 `logback-spring.xml` 文件中的 `<root>` 标签。例如：

