package com.bto.controller;

import com.bto.entity.*;
import com.bto.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class IndexController {

    @Autowired
    private BtoFunctionalInstrumentService btoFunctionalInstrumentService;

    @Autowired
    private BtoCounterService btoCounterService;

    @Autowired
    private BtoFireFightingService btoFireFightingService;

    @Autowired
    private BtoInfraredMeterService btoInfraredMeterService;

    @Autowired
    private BtoInverterAlarmService btoInverterAlarmService;

    @Autowired
    private BtoInverterService btoInverterService;

    @Autowired
    private BtoSensorDataService btoSensorDataService;

    @GetMapping
    public String index() {
        return "Hello World";
    }

    @GetMapping("test")
    public HashMap<String, Object> test() {
        HashMap<String, Object> hashMap = new HashMap<>();
        List<BtoFunctionalInstrumentEntity> functionalInstrumentList = btoFunctionalInstrumentService.getFunctionalInstrumentList();
        hashMap.put("functionalInstrumentList", functionalInstrumentList);
        List<BtoCounterEntity> counterList = btoCounterService.getCounterList();
        hashMap.put("counterList", counterList);
        List<BtoFireFightingEntity> fireFightingList = btoFireFightingService.getFireFightingList();
        hashMap.put("fireFightingList", fireFightingList);
        List<BtoInfraredMeterEntity> infraredMeterList = btoInfraredMeterService.getMeterDataList();
        hashMap.put("infraredMeterList", infraredMeterList);
        List<BtoInverterEntity> inverterList = btoInverterService.getTenMinutesInverterList();
        hashMap.put("inverterList", inverterList);
        List<BtoSensorDataEntity> sensorDataList = btoSensorDataService.getSensorDataList();
        hashMap.put("sensorDataList", sensorDataList);
        List<BtoInverterAlarmEntity> todayInverterAlarmList = btoInverterAlarmService.getTodayInverterAlarmList();
        hashMap.put("todayInverterAlarmList", todayInverterAlarmList);
        return hashMap;
    }
}