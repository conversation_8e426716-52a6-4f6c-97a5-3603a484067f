package com.bto.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.bto.base.BaseDao;
import com.bto.entity.BtoCounterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 并网柜电压电流
*
* <AUTHOR> 
* @since 1.0.0 2024-10-07
*/
@Mapper
public interface BtoCounterDao extends BaseDao<BtoCounterEntity> {

    @DS("cloud")
    void insertCounterList(@Param("list") List<BtoCounterEntity> list);
}