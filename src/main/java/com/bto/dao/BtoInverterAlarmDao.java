package com.bto.dao;

import com.bto.base.BaseDao;
import com.bto.entity.BtoInverterAlarmEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 逆变器故障信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-23
 */
@Mapper
public interface BtoInverterAlarmDao extends BaseDao<BtoInverterAlarmEntity> {

    List<BtoInverterAlarmEntity> getTodayInverterAlarmList(@Param("time") String time);

    List<BtoInverterAlarmEntity> getRecoveryAlarmList();
}