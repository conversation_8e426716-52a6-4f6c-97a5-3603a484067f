package com.bto.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.bto.base.BaseDao;
import com.bto.entity.BtoInverterEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 逆变器实时数据数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-21
 */
@Mapper
public interface BtoInverterDao extends BaseDao<BtoInverterEntity> {
    List<BtoInverterEntity> getTenMinutesList(@Param("tableName") String tableName, @Param("tenMinutesAgo") String tenMinutesAgo, @Param("dateEnd") String dateEnd);

    @DS("cloud")
    void insertInverter(@Param("tableName") String tableName, @Param("inverter") BtoInverterEntity inverter);

    @DS("cloud")
    BtoInverterEntity getListByInverterSnAndTime(@Param("tableName") String tableName, @Param("inverterSn") String inverterSn, @Param("initTime") String initTime);

    @DS("cloud")
    List<BtoInverterEntity> getCloudInverterListByStatus();
}