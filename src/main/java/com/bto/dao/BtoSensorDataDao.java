package com.bto.dao;

import com.bto.base.BaseDao;
import com.bto.entity.BtoSensorDataEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 传感器数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-07
 */
@Mapper
public interface BtoSensorDataDao extends BaseDao<BtoSensorDataEntity> {

    List<BtoSensorDataEntity> getSensorDataList(@Param("time") String time);
}