package com.bto.entity;

import com.bto.base.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 并网柜电压电流
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-07
 */

@Data
@TableName("bto_counter")
public class BtoCounterEntity {
    /**
     * 并网柜id
     */
    private String cabinetId;

    /**
     * 数据采样时间（格式||根据采样频率设置整十分钟：2000-01-01 10:10:00）
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date collectTime;

    /**
     * 电压A
     */
    private Integer voltA;

    /**
     * 电压B
     */
    private Integer voltB;

    /**
     * 电压C
     */
    private Integer voltC;

    /**
     * 电流A
     */
    private Integer currA;

    /**
     * 电流B
     */
    private Integer currB;

    /**
     * 电流C
     */
    private Integer currC;

    /**
     * 状态
     */
    private String status;
}