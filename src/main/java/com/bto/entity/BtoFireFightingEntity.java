package com.bto.entity;

import com.bto.base.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 消防联动
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-09
 */

@Data
@TableName("bto_fire_fighting")
public class BtoFireFightingEntity {
    /**
     * id
     */
    private String id;

    /**
     * 状态
     */
    private String status;

    /**
     * 时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date time;

    /**
     * 区域
     */
    private String region;

}