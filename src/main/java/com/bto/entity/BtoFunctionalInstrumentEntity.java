package com.bto.entity;

import com.bto.base.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 多功能仪表数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-09
 */

@Data
@TableName("bto_functional_instrument")
public class BtoFunctionalInstrumentEntity {
    /**
     * 设备编号（设备SN码）||逆变器和非三晶运维器
     */
    private String deviceId;

    /**
     * 发电量(kWh)*100
     */
    private Integer electricity;

    /**
     * 数据时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date initTime;

    /**
     * A点电压
     */
    private Integer apv;

    /**
     * B点电压
     */
    private Integer bpv;

    /**
     * C点电压
     */
    private Integer cpv;

    /**
     * A点电流
     */
    private Integer aac;

    /**
     * B点电流
     */
    private Integer bac;

    /**
     * C点电流
     */
    private Integer cac;

    /**
     * 数据创建时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 数据更新时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date updateTime;

}