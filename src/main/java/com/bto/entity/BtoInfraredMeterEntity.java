package com.bto.entity;

import com.bto.base.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 红外抄表数据（供电电表数据）
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-28
 */

@Data
@TableName("bto_infrared_meter")
public class BtoInfraredMeterEntity {
    /**
     * 电站uid
     */
    private String plantUid;

    /**
     * 电表id
     */
    @TableId
    private String voltmeterId;

    /**
     * 数据时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date initTime;

    /**
     * 正向有功总电能（实际数据 * 100）|| 用市电||1
     */
    private Integer totalImpep;

    /**
     * 反向有功总电能（实际数据 * 100）|| 光伏卖电||2
     */
    private Integer totalExpep;

    /**
     * 尖峰  正向有功电能（实际数据 * 100）|| 用市电||3
     */
    private Integer sharpPeakImpep;

    /**
     * 尖峰  反向有功电能（实际数据 * 100）|| 光伏卖电||4
     */
    private Integer sharpPeakExpep;

    /**
     * 高峰  正向有功电能（实际数据 * 100）|| 用市电||5
     */
    private Integer peakImpep;

    /**
     * 高峰  反向有功电能（实际数据 * 100）|| 光伏卖电||6
     */
    private Integer peakExpep;

    /**
     * 平段  正向有功电能（实际数据 * 100）|| 用市电||7
     */
    private Long normalImpep;

    /**
     * 平段  反向有功电能（实际数据 * 100）|| 光伏卖电||8
     */
    private Integer normalExpep;

    /**
     * 低谷  正向有功电能（实际数据 * 100）|| 用市电||9
     */
    private Integer valleyImpep;

    /**
     * 低谷  反向有功电能（实际数据 * 100）|| 光伏卖电||10
     */
    private Integer valleyExpep;

    /**
     * A点电压（实际数据 * 100）
     */
    private Integer apv;

    /**
     * B点电压（实际数据 * 100）
     */
    private Integer bpv;

    /**
     * C点电压（实际数据 * 100）
     */
    private Integer cpv;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date updateTime;

    public boolean equalsBySnAndTime(BtoInfraredMeterEntity other) {
        return this.voltmeterId.equals(other.voltmeterId) && this.initTime.equals(other.initTime);
    }
}