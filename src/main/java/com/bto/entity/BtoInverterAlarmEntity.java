package com.bto.entity;

import com.bto.base.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 逆变器故障信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-23
 */

@Data
@TableName("bto_inverter_alarm")
public class BtoInverterAlarmEntity {
    /**
     * 逆变器SN
     */
    private String inverterSn;

    /**
     * 报警码
     */
    private String alarmCode;

    /**
     * 报警信息
     */
    private String alarmInfo;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date endTime;

    /**
     * 状态（0未处理，1已处理 , 2失效）
     */
    private Integer status;

    /**
     * 三晶数据标识（三晶：0  ，自取：1）
     */
    private String state;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date updateTime;

    /**
     * 数据日期
     */
    private Date alarmDate;

    public void setState(String state) {
        this.state = "2";
    }

    public boolean equalsBySnAndTime(BtoInverterAlarmEntity other) {
        return this.inverterSn.equals(other.inverterSn) && this.startTime.equals(other.startTime);
    }

    public String generateKey() {
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.DATE_PATTERN_FIGURE);
        return this.inverterSn + "|" + this.alarmCode + "|" + sdf.format(this.alarmDate);
    }

}