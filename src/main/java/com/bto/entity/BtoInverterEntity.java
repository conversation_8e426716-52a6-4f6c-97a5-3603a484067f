package com.bto.entity;

import com.bto.base.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 逆变器实时数据数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-21
 */
@Data
public class BtoInverterEntity {
    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 逆变器SN
     */
    private String inverterSn;

    /**
     * 功率（W）
     */
    private Integer power;

    /**
     * 当天发电量（kWh）*100
     */
    private Integer todayElectricity;

    /**
     * 当月发电量（kWh）*100
     */
    private Integer monthElectricity;

    /**
     * 当年发电量（kWh）*100
     */
    private Integer yearElectricity;

    /**
     * 累计发电量（kWh）*100
     */
    private Integer totalElectricity;

    /**
     * 数据时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date initTime;

    /**
     * 输入电流1路（A）*100
     */
    private Integer ipv1;

    /**
     * 输入电流2路（A）*100
     */
    private Integer ipv2;

    /**
     * 输入电流3路（A）*100
     */
    private Integer ipv3;

    /**
     * 输入电流4路（A）*100
     */
    private Integer ipv4;

    /**
     * 输入电流5路（A）*100
     */
    private Integer ipv5;

    /**
     * 输入电流6路（A）*100
     */
    private Integer ipv6;

    /**
     * 输入电流7路（A）*100
     */
    private Integer ipv7;

    /**
     * 输入电流8路（A）*100
     */
    private Integer ipv8;

    /**
     * 输入电流9路（A）*100
     */
    private Integer ipv9;

    /**
     * 输入电流10路（A）*100
     */
    private Integer ipv10;

    /**
     * 输入电流11路（A）*100
     */
    private Integer ipv11;

    /**
     * 输入电流12路（A）*100
     */
    private Integer ipv12;

    /**
     * 输入电压1路（V）*100
     */
    private Integer vpv1;

    /**
     * 输入电压2路（V）*100
     */
    private Integer vpv2;

    /**
     * 输入电压3路（V）*100
     */
    private Integer vpv3;

    /**
     * 输入电压4路（V）*100
     */
    private Integer vpv4;

    /**
     * 输入电压5路（V）*100
     */
    private Integer vpv5;

    /**
     * 输入电压6路（V）*100
     */
    private Integer vpv6;

    /**
     * 输入电压7路（V）*100
     */
    private Integer vpv7;

    /**
     * 输入电压8路（V）*100
     */
    private Integer vpv8;

    /**
     * 输入电压9路（V）*100
     */
    private Integer vpv9;

    /**
     * 输入电压10路（V）*100
     */
    private Integer vpv10;

    /**
     * 输入电压11路（V）*100
     */
    private Integer vpv11;

    /**
     * 输入电压12路（V）*100
     */
    private Integer vpv12;

    /**
     * 输出电流1路（A）*100
     */
    private Integer iac1;

    /**
     * 输出电流2路（A）*100
     */
    private Integer iac2;

    /**
     * 输出电流3路（A）*100
     */
    private Integer iac3;

    /**
     * 输出电压1路（V）*100
     */
    private Integer vac1;

    /**
     * 输出电压2路（V）*100
     */
    private Integer vac2;

    /**
     * 输出电压3路（V）*100
     */
    private Integer vac3;

    /**
     * 温度（℃）
     */
    private String temp;

    /**
     * 频率1（Hz）*100
     */
    private Integer fac1;

    /**
     * 频率2（Hz）*100
     */
    private Integer fac2;

    /**
     * 频率3（Hz）*100
     */
    private Integer fac3;

    /**
     * pv1功率
     */
    private Integer pv1Power;

    /**
     * pv2功率
     */
    private Integer pv2Power;

    /**
     * pv3功率
     */
    private Integer pv3Power;

    /**
     * pv4功率
     */
    private Integer pv4Power;

    /**
     * pv5功率
     */
    private Integer pv5Power;

    /**
     * pv6功率
     */
    private Integer pv6Power;

    /**
     * pv7功率
     */
    private Integer pv7Power;

    /**
     * pv8功率
     */
    private Integer pv8Power;

    /**
     * pv9功率
     */
    private Integer pv9Power;

    /**
     * pv10功率
     */
    private Integer pv10Power;

    /**
     * pv11功率
     */
    private Integer pv11Power;

    /**
     * pv12功率
     */
    private Integer pv12Power;

    /**
     * 三晶数据标识（三晶：0  ，自取：1）
     */
    private Integer state;

    /**
     * 数据插入时间
     */
    private Date updateTime;

    public void setState(Integer state) {
        this.state = 2;
    }

    public boolean equalsBySnAndTime(BtoInverterEntity other) {
        return this.inverterSn.equals(other.inverterSn) && this.initTime.equals(other.initTime);
    }
}