package com.bto.entity;

import com.bto.base.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

/**
 * 逆变器最新数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-25
 */

@Data
@TableName("bto_inverter_latest")
public class BtoInverterLatestEntity {
    /**
     * 电站uid
     */
    private String plantUid;

    /**
     * 逆变器sn
     */
    @TableId
    private String inverterSn;

    /**
     * 状态（0：离线，1：正常运行，2：告警运行,3:自检提示,5:夜间离线）
     */
    private Integer inverterStatus;

    /**
     * 功率
     */
    private Integer power;

    /**
     * 日发电量(kWh)*100
     */
    private Integer todayElectricity;

    /**
     * 月发电量(kWh)*100
     */
    private Integer monthElectricity;

    /**
     * 年发电量(kWh)*100
     */
    private Integer yearElectricity;

    /**
     * 总发电量(kWh)*100
     */
    private Integer totalElectricity;

    /**
     * 信号强度
     */
    private Integer signalStrength;

    /**
     * 项目专项（1：户用。2：整县，3：）
     */
    private Integer projectSpecial;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 三晶数据标识（三晶：0  ，自取：1，2推送（新华丽））
     */
    private String state;

    /**
     * 数据更新时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date updateTime;

    /**
     * 是否有电表 0 无 1有(仅针对自取数据生效)
     */
    private Integer isElectricityMeter;

    /**
     * 运维器IMEI，仅自取数据存在
     */
    private String deviceImei;

    /**
     * 电站状态（0：存在，1：删除）
     */
    private Integer isDeleted;

    /**
     * 预留--（是否删除：1为删除，空未删除）
     */
    private String res1;

    /**
     * 预留--（是否自取：1为自取数据，空第三方）
     */
    private String res2;

    /**
     * 预留
     */
    private String res3;

}