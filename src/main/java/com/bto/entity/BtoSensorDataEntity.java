package com.bto.entity;

import com.bto.base.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 传感器数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-07
 */

@Data
@TableName("bto_sensor_data")
public class BtoSensorDataEntity {
    /**
     * 电站id
     */
    private String plantUid;

    /**
     * 传感器ID
     */
    private String sensorId;

    /**
     * 烟雾浓度(PPM)
     */
    private Integer smokeConcentr;

    /**
     * 温度(℃)
     */
    private BigDecimal temp;

    /**
     * 湿度（%RH）
     */
    private BigDecimal humidity;

    /**
     * 烟雾报警器状态(0:未告警，1：已告警)
     */
    private Integer alarmStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN, timezone = "GMT+8")
    private Date updateTime;

}