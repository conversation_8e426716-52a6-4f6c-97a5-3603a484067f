package com.bto.job;

import cn.hutool.core.collection.CollUtil;
import com.bto.entity.*;
import com.bto.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SynchronizationTask {

    @Autowired
    private BtoInverterService btoInverterService;

    @Autowired
    private BtoInverterAlarmService btoInverterAlarmService;

    @Autowired
    private BtoInverterLatestService btoInverterLatestService;

    @Autowired
    private BtoInfraredMeterService btoInfraredMeterService;

    @Autowired
    private BtoSensorDataService btoSensorDataService;

    @Autowired
    private BtoCounterService btoCounterService;

    @Autowired
    private BtoFireFightingService btoFireFightingService;

    @Autowired
    private BtoFunctionalInstrumentService btoFunctionalInstrumentService;

    // 五分钟更新一次
    @Scheduled(fixedRate = 1000 * 60 * 5)
    public void executeOne() {
        log.info("==================================================");
        try {
            List<BtoSensorDataEntity> sensorDataList = btoSensorDataService.getSensorDataList();
            if (CollUtil.isNotEmpty(sensorDataList)) {
                btoSensorDataService.insertSensorDataList(sensorDataList);
                log.info("温湿度传感器数据插入成功");
            } else {
                log.info("温湿度传感器数据为空");
            }
        } catch (Exception e) {
            log.error("温湿度传感器数据插入失败");
            log.error(e.getMessage());
        }

        log.info("==================================================");
        try {
            List<BtoFireFightingEntity> fireFightingList = btoFireFightingService.getFireFightingList();
            if (CollUtil.isNotEmpty(fireFightingList)) {
                btoFireFightingService.insertFireFightingList(fireFightingList);
                log.info("消防联动数据插入成功");
            } else {
                log.info("消防联动数据为空");
            }
        } catch (Exception e) {
            log.error("消防联动数据插入失败");
            log.error(e.getMessage());
        }

        log.info("==================================================");
        try {
            List<BtoInverterLatestEntity> latestInverterDataList = btoInverterLatestService.getLatestInverterDataList();
            if (CollUtil.isNotEmpty(latestInverterDataList)) {
                btoInverterLatestService.updateLatestInverterDataList(latestInverterDataList);
                log.info("逆变器最新数据更新成功");
            } else {
                log.info("逆变器最新数据为空");
            }
        } catch (Exception e) {
            log.error("逆变器最新数据插入失败");
            log.error(e.getMessage());
        }
    }

    // 并网柜数据，每三十分钟执行一次，每天5~20点执行
    // @Scheduled(cron = "0 0,30 05-20 * * ? ")
    @Scheduled(fixedDelay = 1000 * 60 * 30)
    public void executeTwo() {
        log.info("==================================================");
        try {
            List<BtoCounterEntity> counterList = btoCounterService.getCounterList();
            if (CollUtil.isNotEmpty(counterList)) {
                btoCounterService.insertCounterList(counterList);
                log.info("并网柜开关数据插入成功");
            } else {
                log.info("并网柜开关数据为空");
            }
        } catch (Exception e) {
            log.error("并网柜开关数据插入失败");
            log.error(e.getMessage());
        }

        log.info("==================================================");
        try {
            List<BtoFunctionalInstrumentEntity> functionalInstrumentList = btoFunctionalInstrumentService.getFunctionalInstrumentList();
            if (CollUtil.isNotEmpty(functionalInstrumentList)) {
                btoFunctionalInstrumentService.insertFunctionalInstrumentList(functionalInstrumentList);
                log.info("多功能仪表数据插入成功");
            } else {
                log.info("多功能仪表数据为空");
            }
        } catch (Exception e) {
            log.error("多功能仪表数据插入失败");
            log.error(e.getMessage());
        }
    }

    // 逆变器数据，每十分钟执行一次，每天5~19点执行
    // @Scheduled(cron = "0 5,15,25,35,45,55 05-19 * * ?")
    @Scheduled(fixedDelay = 1000 * 60 * 10)
    public void executeThree() {
        log.info("==================================================");
        try {
            List<BtoInverterEntity> tenMinutesInverterList = btoInverterService.getTenMinutesInverterList();
            if (CollUtil.isNotEmpty(tenMinutesInverterList)) {
                List<BtoInverterEntity> listByIds = btoInverterService.getListByInverterSnAndTime(tenMinutesInverterList);
                if (CollUtil.isNotEmpty(listByIds)) {
                    tenMinutesInverterList = tenMinutesInverterList.stream()
                            .filter(a -> listByIds.stream().noneMatch(b -> b.equalsBySnAndTime(a)))
                            .collect(Collectors.toList());
                }
            }
            if (CollUtil.isNotEmpty(tenMinutesInverterList)) {
                btoInverterService.insertInverterList(tenMinutesInverterList);
                log.info("逆变器实时数据插入成功");
            } else {
                log.info("逆变器无新实时数据");
            }
        } catch (Exception e) {
            log.error("逆变器实时数据插入失败");
            log.error(e.getMessage());
        }

        log.info("==================================================");
        try {
            List<BtoInverterAlarmEntity> todayInverterAlarmList = btoInverterAlarmService.getTodayInverterAlarmList();
            if (CollUtil.isNotEmpty(todayInverterAlarmList)) {
                List<BtoInverterAlarmEntity> listByIds = btoInverterAlarmService.getAlarmListByInverterSnAndTime(todayInverterAlarmList);
                btoInverterAlarmService.updateExistingRecoveryAlarmList(todayInverterAlarmList);
                if (CollUtil.isNotEmpty(listByIds)) {
                    todayInverterAlarmList = todayInverterAlarmList.stream()
                            .filter(a -> listByIds.stream().noneMatch(b -> b.equalsBySnAndTime(a)))
                            .collect(Collectors.toList());
                }
            }
            if (CollUtil.isNotEmpty(todayInverterAlarmList)) {
                btoInverterAlarmService.insertInverterAlarmList(todayInverterAlarmList);
            } else {
                log.info("无新告警数据");
            }
        } catch (Exception e) {
            log.error("逆变器告警插入失败");
            log.error(e.getMessage());
        }

        log.info("==================================================");
        try {
            List<BtoInfraredMeterEntity> list = btoInfraredMeterService.getMeterDataList();
            if (CollUtil.isNotEmpty(list)) {
                List<BtoInfraredMeterEntity> entities = btoInfraredMeterService.getListByVoltmeterIdAndTime(list);
                if (CollUtil.isNotEmpty(entities)) {
                    list = list.stream()
                            .filter(a -> entities.stream().noneMatch(b -> b.equalsBySnAndTime(a)))
                            .collect(Collectors.toList());
                }
            }
            if (CollUtil.isNotEmpty(list)) {
                btoInfraredMeterService.insertMeterList(list);
                log.info("电表数据插入成功");
            } else {
                log.info("电表数据为空");
            }
        } catch (Exception e) {
            log.error("电表数据插入失败");
            log.error(e.getMessage());
        }
    }

}