package com.bto.service;

import com.bto.base.BaseService;
import com.bto.entity.BtoFunctionalInstrumentEntity;

import java.util.List;

/**
 * 多功能仪表数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-09
 */
public interface BtoFunctionalInstrumentService extends BaseService<BtoFunctionalInstrumentEntity> {

    List<BtoFunctionalInstrumentEntity> getFunctionalInstrumentList();

    void insertFunctionalInstrumentList(List<BtoFunctionalInstrumentEntity> list);
}