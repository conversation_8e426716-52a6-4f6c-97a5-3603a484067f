package com.bto.service;

import com.bto.base.BaseService;
import com.bto.entity.BtoInfraredMeterEntity;

import java.util.List;

/**
 * 红外抄表数据（供电电表数据）
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-28
 */
public interface BtoInfraredMeterService extends BaseService<BtoInfraredMeterEntity> {
    List<BtoInfraredMeterEntity> getMeterDataList();

    List<BtoInfraredMeterEntity> getListByVoltmeterIdAndTime(List<BtoInfraredMeterEntity> list);

    void insertMeterList(List<BtoInfraredMeterEntity> list);
}