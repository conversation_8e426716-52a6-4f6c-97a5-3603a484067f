package com.bto.service;

import com.bto.base.BaseService;
import com.bto.entity.BtoInverterAlarmEntity;

import java.util.List;

/**
 * 逆变器故障信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-23
 */
public interface BtoInverterAlarmService extends BaseService<BtoInverterAlarmEntity> {

    List<BtoInverterAlarmEntity> getTodayInverterAlarmList();

    List<BtoInverterAlarmEntity> getAlarmListByInverterSnAndTime(List<BtoInverterAlarmEntity> list);

    void insertInverterAlarmList(List<BtoInverterAlarmEntity> inverterAlarmList);

    void updateExistingRecoveryAlarmList(List<BtoInverterAlarmEntity> todayInverterAlarmList);

    List<BtoInverterAlarmEntity> getRecoveryAlarmList();
}