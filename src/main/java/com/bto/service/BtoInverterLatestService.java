package com.bto.service;

import com.bto.base.BaseService;
import com.bto.entity.BtoInverterLatestEntity;

import java.util.List;

/**
 * 逆变器最新数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-25
 */
public interface BtoInverterLatestService extends BaseService<BtoInverterLatestEntity> {

    List<BtoInverterLatestEntity> getLatestInverterDataList();

    void updateLatestInverterDataList(List<BtoInverterLatestEntity> inverterDataList);

}