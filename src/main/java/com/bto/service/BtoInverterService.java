package com.bto.service;

import com.bto.base.BaseService;
import com.bto.entity.BtoInverterEntity;

import java.util.List;

/**
 * 逆变器实时数据数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-21
 */
public interface BtoInverterService extends BaseService<BtoInverterEntity> {
    List<BtoInverterEntity> getTenMinutesInverterList();

    void insertInverterList(List<BtoInverterEntity> list);

    List<BtoInverterEntity> getListByInverterSnAndTime(List<BtoInverterEntity> tenMinutesInverterList);

    List<BtoInverterEntity> getCloudInverterListByStatus();
}