package com.bto.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.base.BaseServiceImpl;
import com.bto.base.DateUtils;
import lombok.AllArgsConstructor;
import com.bto.entity.BtoCounterEntity;
import com.bto.dao.BtoCounterDao;
import com.bto.service.BtoCounterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 并网柜电压电流
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-07
 */
@Service
@AllArgsConstructor
public class BtoCounterServiceImpl extends BaseServiceImpl<BtoCounterDao, BtoCounterEntity> implements BtoCounterService {

    @Autowired
    private BtoCounterDao btoCounterDao;

    @Override
    public List<BtoCounterEntity> getCounterList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN);
        String time = LocalDateTime.now().minusMinutes(10).format(formatter);
        QueryWrapper<BtoCounterEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().gt(BtoCounterEntity::getCollectTime, time);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void insertCounterList(List<BtoCounterEntity> list) {
        for (BtoCounterEntity btoCounterEntity : list) {
            btoCounterEntity.setCurrA(btoCounterEntity.getCurrA() * 100);
            btoCounterEntity.setCurrB(btoCounterEntity.getCurrB() * 100);
            btoCounterEntity.setCurrC(btoCounterEntity.getCurrC() * 100);
            btoCounterEntity.setVoltA(btoCounterEntity.getVoltA() * 100);
            btoCounterEntity.setVoltB(btoCounterEntity.getVoltB() * 100);
            btoCounterEntity.setVoltC(btoCounterEntity.getVoltC() * 100);
        }
        btoCounterDao.insertCounterList(list);
    }
}