package com.bto.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.base.BaseServiceImpl;
import com.bto.base.DateUtils;
import com.bto.dao.BtoFireFightingDao;
import com.bto.entity.BtoSensorDataEntity;
import lombok.AllArgsConstructor;
import com.bto.entity.BtoFireFightingEntity;
import com.bto.service.BtoFireFightingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 消防联动
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-09
 */
@Service
@AllArgsConstructor
public class BtoFireFightingServiceImpl extends BaseServiceImpl<BtoFireFightingDao, BtoFireFightingEntity> implements BtoFireFightingService {

    @Override
    public List<BtoFireFightingEntity> getFireFightingList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN);
        String time = LocalDateTime.now().minusMinutes(5).format(formatter);
        QueryWrapper<BtoFireFightingEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().gt(BtoFireFightingEntity::getTime, time);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void insertFireFightingList(List<BtoFireFightingEntity> list) {
        for (BtoFireFightingEntity btoFireFightingEntity : list) {
            baseMapper.insert(btoFireFightingEntity);
        }
    }
}