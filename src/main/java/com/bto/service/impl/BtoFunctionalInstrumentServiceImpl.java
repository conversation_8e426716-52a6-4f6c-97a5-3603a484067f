package com.bto.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.base.BaseServiceImpl;
import com.bto.base.DateUtils;
import com.bto.entity.BtoSensorDataEntity;
import lombok.AllArgsConstructor;
import com.bto.entity.BtoFunctionalInstrumentEntity;
import com.bto.dao.BtoFunctionalInstrumentDao;
import com.bto.service.BtoFunctionalInstrumentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 多功能仪表数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-09
 */
@Service
@AllArgsConstructor
public class BtoFunctionalInstrumentServiceImpl extends BaseServiceImpl<BtoFunctionalInstrumentDao, BtoFunctionalInstrumentEntity> implements BtoFunctionalInstrumentService {

    @Override
    public List<BtoFunctionalInstrumentEntity> getFunctionalInstrumentList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN);
        String time = LocalDateTime.now().minusMinutes(15).format(formatter);
        QueryWrapper<BtoFunctionalInstrumentEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().gt(BtoFunctionalInstrumentEntity::getInitTime, time);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void insertFunctionalInstrumentList(List<BtoFunctionalInstrumentEntity> list) {
        for (BtoFunctionalInstrumentEntity btoFunctionalInstrumentEntity : list) {
            baseMapper.insert(btoFunctionalInstrumentEntity);
        }
    }
}