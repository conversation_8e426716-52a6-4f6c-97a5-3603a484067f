package com.bto.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.base.BaseServiceImpl;
import com.bto.base.DateUtils;
import lombok.AllArgsConstructor;
import com.bto.entity.BtoInfraredMeterEntity;
import com.bto.dao.BtoInfraredMeterDao;
import com.bto.service.BtoInfraredMeterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 红外抄表数据（供电电表数据）
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-28
 */
@Service
@AllArgsConstructor
public class BtoInfraredMeterServiceImpl extends BaseServiceImpl<BtoInfraredMeterDao, BtoInfraredMeterEntity> implements BtoInfraredMeterService {

    @Override
    public List<BtoInfraredMeterEntity> getMeterDataList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN);
        String time = LocalDateTime.now().minusMinutes(10).format(formatter);
        Date date = new Date();
        QueryWrapper<BtoInfraredMeterEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("init_time", DateUtil.beginOfDay(date).toString(), DateUtil.endOfDay(date).toString());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @DS("cloud")
    public List<BtoInfraredMeterEntity> getListByVoltmeterIdAndTime(List<BtoInfraredMeterEntity> list) {
        ArrayList<BtoInfraredMeterEntity> meterEntities = new ArrayList<>();
        for (BtoInfraredMeterEntity entity : list) {
            QueryWrapper<BtoInfraredMeterEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BtoInfraredMeterEntity::getInitTime, entity.getInitTime())
                    .eq(BtoInfraredMeterEntity::getVoltmeterId, entity.getVoltmeterId());
            BtoInfraredMeterEntity btoInfraredMeterEntity = baseMapper.selectOne(queryWrapper);
            if (Objects.nonNull(btoInfraredMeterEntity)) {
                meterEntities.add(btoInfraredMeterEntity);
            }
        }
        return meterEntities;
    }

    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void insertMeterList(List<BtoInfraredMeterEntity> list) {
        for (BtoInfraredMeterEntity btoInfraredMeterEntity : list) {
            baseMapper.insert(btoInfraredMeterEntity);
        }
    }
}