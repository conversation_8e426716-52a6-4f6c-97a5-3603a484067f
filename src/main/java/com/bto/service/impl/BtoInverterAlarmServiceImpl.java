package com.bto.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.base.BaseServiceImpl;
import com.bto.base.DateUtils;
import lombok.AllArgsConstructor;
import com.bto.entity.BtoInverterAlarmEntity;
import com.bto.dao.BtoInverterAlarmDao;
import com.bto.service.BtoInverterAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 逆变器故障信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-23
 */
@Slf4j
@Service
@AllArgsConstructor
public class BtoInverterAlarmServiceImpl extends BaseServiceImpl<BtoInverterAlarmDao, BtoInverterAlarmEntity> implements BtoInverterAlarmService {


    @Autowired
    private BtoInverterAlarmDao btoInverterAlarmDao;

    @Override
    public List<BtoInverterAlarmEntity> getTodayInverterAlarmList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN);
        String time = LocalDateTime.now().minusMinutes(10).format(formatter);
        Date date = new Date();
        return btoInverterAlarmDao.getTodayInverterAlarmList(time);
    }

    @Override
    @DS("cloud")
    public List<BtoInverterAlarmEntity> getAlarmListByInverterSnAndTime(List<BtoInverterAlarmEntity> list) {
        ArrayList<BtoInverterAlarmEntity> arrayList = new ArrayList<>();
        for (BtoInverterAlarmEntity entity : list) {
            String format = new SimpleDateFormat(DateUtils.DATE_TIME_PATTERN).format(entity.getStartTime());
            QueryWrapper<BtoInverterAlarmEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BtoInverterAlarmEntity::getInverterSn, entity.getInverterSn());
            queryWrapper.lambda().eq(BtoInverterAlarmEntity::getStartTime, format);
            BtoInverterAlarmEntity inverterAlarm = btoInverterAlarmDao.selectOne(queryWrapper);
            if (Objects.nonNull(inverterAlarm)) {
                arrayList.add(inverterAlarm);
            }
        }
        return arrayList;
    }


    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void insertInverterAlarmList(List<BtoInverterAlarmEntity> inverterAlarmList) {
        for (BtoInverterAlarmEntity btoInverterAlarmEntity : inverterAlarmList) {
            baseMapper.insert(btoInverterAlarmEntity);
        }
        log.info("逆变器告警插入成功");
    }

    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void updateExistingRecoveryAlarmList(List<BtoInverterAlarmEntity> todayInverterAlarmList) {
        List<BtoInverterAlarmEntity> recoveryAlarmList = getRecoveryAlarmList();
        if (CollUtil.isNotEmpty(recoveryAlarmList)) {
            Map<String, BtoInverterAlarmEntity> map = new HashMap<>();
            for (BtoInverterAlarmEntity entity : todayInverterAlarmList) {
                String key = entity.generateKey();
                map.put(key, entity);
            }
            List<BtoInverterAlarmEntity> duplicates = new ArrayList<>();
            for (BtoInverterAlarmEntity entity : recoveryAlarmList) {
                String key = entity.generateKey();
                if (map.containsKey(key)) {
                    duplicates.add(entity);
                }
            }
            for (BtoInverterAlarmEntity duplicate : duplicates) {
                duplicate.setStatus(0);
                duplicate.setCreateTime(new Date());
                duplicate.setEndTime(new Date());
                baseMapper.update(duplicate, null);
            }
            log.info("逆变器告警更新成功");
        }
    }

    @Override
    @DS("cloud")
    public List<BtoInverterAlarmEntity> getRecoveryAlarmList() {
        return btoInverterAlarmDao.getRecoveryAlarmList();
    }
}