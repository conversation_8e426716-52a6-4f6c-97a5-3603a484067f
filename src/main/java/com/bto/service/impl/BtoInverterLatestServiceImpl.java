package com.bto.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bto.base.BaseServiceImpl;
import lombok.AllArgsConstructor;
import com.bto.entity.BtoInverterLatestEntity;
import com.bto.dao.BtoInverterLatestDao;
import com.bto.service.BtoInverterLatestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 逆变器最新数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-25
 */
@Service
@Slf4j
@AllArgsConstructor
public class BtoInverterLatestServiceImpl extends BaseServiceImpl<BtoInverterLatestDao, BtoInverterLatestEntity> implements BtoInverterLatestService {

    @Override
    public List<BtoInverterLatestEntity> getLatestInverterDataList() {
        return baseMapper.selectList(null);
    }

    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void updateLatestInverterDataList(List<BtoInverterLatestEntity> inverterDataList) {
        if (CollUtil.isNotEmpty(getLatestInverterDataList())) {
            for (BtoInverterLatestEntity btoInverterLatestEntity : inverterDataList) {
                UpdateWrapper<BtoInverterLatestEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(BtoInverterLatestEntity::getInverterSn, btoInverterLatestEntity.getInverterSn());
                updateWrapper.lambda().set(BtoInverterLatestEntity::getInverterStatus, btoInverterLatestEntity.getInverterStatus());
                updateWrapper.lambda().set(BtoInverterLatestEntity::getPower, btoInverterLatestEntity.getPower());
                updateWrapper.lambda().set(BtoInverterLatestEntity::getTodayElectricity, btoInverterLatestEntity.getTodayElectricity());
                updateWrapper.lambda().set(BtoInverterLatestEntity::getMonthElectricity, btoInverterLatestEntity.getMonthElectricity());
                updateWrapper.lambda().set(BtoInverterLatestEntity::getYearElectricity, btoInverterLatestEntity.getYearElectricity());
                updateWrapper.lambda().set(BtoInverterLatestEntity::getTotalElectricity, btoInverterLatestEntity.getTotalElectricity());
                updateWrapper.lambda().set(BtoInverterLatestEntity::getUpdateTime, btoInverterLatestEntity.getUpdateTime());
                baseMapper.update(new BtoInverterLatestEntity(), updateWrapper);
            }
        } else {
            for (BtoInverterLatestEntity btoInverterLatestEntity : inverterDataList) {
                baseMapper.insert(btoInverterLatestEntity);
            }
            log.info("逆变器最新数据插入成功");
        }
    }
}