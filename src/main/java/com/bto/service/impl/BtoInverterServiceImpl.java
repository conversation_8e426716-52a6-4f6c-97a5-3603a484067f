package com.bto.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.bto.base.BaseServiceImpl;
import com.bto.base.DateUtils;
import com.bto.dao.BtoInverterDao;
import com.bto.entity.BtoInverterEntity;
import com.bto.service.BtoInverterService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 逆变器实时数据数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-09-21
 */
@Service
@AllArgsConstructor
public class BtoInverterServiceImpl extends BaseServiceImpl<BtoInverterDao, BtoInverterEntity> implements BtoInverterService {

    @Autowired
    private BtoInverterDao btoInverterDao;

    @Override
    public List<BtoInverterEntity> getTenMinutesInverterList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN);
        String time = LocalDateTime.now().minusMinutes(10).format(formatter);
        Date date = new Date();
        String beginOfDay = DateUtil.beginOfDay(date).toString();
        return btoInverterDao.getTenMinutesList(getLocalTableName(), time, DateUtil.endOfDay(date).toString());
    }

    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void insertInverterList(List<BtoInverterEntity> list) {
        for (BtoInverterEntity btoInverterEntity : list) {
            btoInverterDao.insertInverter(getCloudTableName(), btoInverterEntity);
        }
    }

    @DS("cloud")
    @Override
    public List<BtoInverterEntity> getListByInverterSnAndTime(List<BtoInverterEntity> tenMinutesInverterList) {
        ArrayList<BtoInverterEntity> btoInverterEntities = new ArrayList<>();
        for (BtoInverterEntity entity : tenMinutesInverterList) {
            String format = new SimpleDateFormat(DateUtils.DATE_TIME_PATTERN).format(entity.getInitTime());
            BtoInverterEntity inverter = btoInverterDao.getListByInverterSnAndTime(getCloudTableName(), entity.getInverterSn(), format);
            if (Objects.nonNull(inverter)) {
                btoInverterEntities.add(inverter);
            }
        }
        return btoInverterEntities;
    }

    @Override
    @DS("cloud")
    public List<BtoInverterEntity> getCloudInverterListByStatus() {
        return btoInverterDao.getCloudInverterListByStatus();
    }

    String getLocalTableName() {
        String tableName = "bto_inverter_";
        tableName = tableName + DateUtil.thisYear();
        return tableName;
    }

    String getCloudTableName() {
        String tableName = "bto_inverter_";
        tableName = tableName + DateUtil.format(new Date(), "yyyyMMdd");
        return tableName;
    }
}