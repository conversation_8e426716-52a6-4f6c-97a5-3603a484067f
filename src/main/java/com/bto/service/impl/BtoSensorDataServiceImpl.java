package com.bto.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.base.BaseServiceImpl;
import com.bto.base.DateUtils;
import com.bto.dao.BtoSensorDataDao;
import com.bto.entity.BtoSensorDataEntity;
import com.bto.service.BtoSensorDataService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 传感器数据
 *
 * <AUTHOR>
 * @since 1.0.0 2024-10-07
 */
@Service
@AllArgsConstructor
public class BtoSensorDataServiceImpl extends BaseServiceImpl<BtoSensorDataDao, BtoSensorDataEntity> implements BtoSensorDataService {

    @Override
    public List<BtoSensorDataEntity> getSensorDataList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN);
        String time = LocalDateTime.now().minusMinutes(6).format(formatter);
        QueryWrapper<BtoSensorDataEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().gt(BtoSensorDataEntity::getCreateTime, time);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @DS("cloud")
    @Transactional(rollbackFor = Exception.class)
    public void insertSensorDataList(List<BtoSensorDataEntity> list) {
        for (BtoSensorDataEntity btoSensorDataEntity : list) {
            baseMapper.insert(btoSensorDataEntity);
        }
    }
}