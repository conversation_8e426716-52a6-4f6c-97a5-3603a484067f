server:
  port: 8228

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    name: SynchronizeData
  task:
    scheduling:
      pool:
        size: 10
      thread-name-prefix: sync-task
      shutdown:
        # 等待所有任务完成再终止
        await-termination: true
        await-termination-period: 60s
  datasource:
    dynamic:
      hikari:
        connection-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 15
        min-idle: 5
        connection-test-query: select 1
        pool-name: BtoHikariCP
      primary: local
      datasource:
        local:
          username: root
          password: bto_xhl++
#          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************************************
        cloud:
          username: bto_guangyun
          password: BotongMysql666+
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************************************************************

#          username: root
#          password: 123456
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: ***********************************************************************************************************************************************

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.bto.entity
  global-config:
    # 数据库相关配置
    db-config:
      # ID自增
      id-type: AUTO
      # 逻辑已删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
    banner: false
  # 原生配置
  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: "null"
  configuration-properties:
    prefix:
    blobType: BLOB
    boolValue: TRUE

logging:
  level:
    root: info