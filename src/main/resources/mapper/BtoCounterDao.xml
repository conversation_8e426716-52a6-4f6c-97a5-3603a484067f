<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.dao.BtoCounterDao">

    <resultMap type="com.bto.entity.BtoCounterEntity" id="btoCounterMap">
        <result property="cabinetId" column="cabinet_id"/>
        <result property="collectTime" column="collect_time"/>
        <result property="voltA" column="volt_a"/>
        <result property="voltB" column="volt_b"/>
        <result property="voltC" column="volt_c"/>
        <result property="currA" column="curr_a"/>
        <result property="currB" column="curr_b"/>
        <result property="currC" column="curr_c"/>
        <result property="status" column="status"/>
    </resultMap>
    <insert id="insertCounterList">
        insert into bto_counter(cabinet_id,collect_time, apv, bpv, cpv, aac, bac, cac, status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.cabinetId},#{item.collectTime},#{item.voltA},#{item.voltB},#{item.voltC},#{item.currA},#{item.currB},#{item.currC},#{item.status})
        </foreach>
    </insert>
</mapper>