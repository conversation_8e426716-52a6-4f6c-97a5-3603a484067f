<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.dao.BtoInfraredMeterDao">
    <resultMap type="com.bto.entity.BtoInfraredMeterEntity" id="btoInfraredMeterMap">
        <result property="plantUid" column="plant_uid"/>
        <result property="voltmeterId" column="voltmeter_id"/>
        <result property="initTime" column="init_time"/>
        <result property="totalImpep" column="total_impep"/>
        <result property="totalExpep" column="total_expep"/>
        <result property="sharpPeakImpep" column="sharp_peak_impep"/>
        <result property="sharpPeakExpep" column="sharp_peak_expep"/>
        <result property="peakImpep" column="peak_impep"/>
        <result property="peakExpep" column="peak_expep"/>
        <result property="normalImpep" column="normal_impep"/>
        <result property="normalExpep" column="normal_expep"/>
        <result property="valleyImpep" column="valley_impep"/>
        <result property="valleyExpep" column="valley_expep"/>
        <result property="apv" column="apv"/>
        <result property="bpv" column="bpv"/>
        <result property="cpv" column="cpv"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>