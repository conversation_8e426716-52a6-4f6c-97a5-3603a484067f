<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.dao.BtoInverterAlarmDao">

    <resultMap type="com.bto.entity.BtoInverterAlarmEntity" id="btoInverterAlarmMap">
        <result property="inverterSn" column="inverter_sn"/>
        <result property="alarmCode" column="alarm_code"/>
        <result property="alarmInfo" column="alarm_info"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="status" column="status"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="alarmDate" column="alarm_date"/>
    </resultMap>
    <select id="getTodayInverterAlarmList" resultType="com.bto.entity.BtoInverterAlarmEntity">
        select id,
               inverter_sn,
               alarm_code,
               alarm_info,
               start_time,
               end_time,
               status,
               state,
               create_time,
               update_time, DATE (alarm_date) AS alarmDate
        from
            bto_inverter_alarm
        where
            date (start_time) = curdate()
        and status = 0
    </select>
    <select id="getRecoveryAlarmList" resultType="com.bto.entity.BtoInverterAlarmEntity">
        SELECT
            inverter_sn,
            alarm_code,
            alarm_date
        FROM
            bto_inverter_alarm t1
        WHERE
                inverter_sn IN (
                SELECT
                    device_id
                FROM
                    bto_device
                WHERE
                    is_deleted = 0
                  AND device_type = 1
                  AND plant_uid IN ( 'BTOPLANT-20240612-INDUSTRY-COMMERCE-0001', 'BTOPLANT-20240920-INDUSTRY-COMMERCE-0002' ))
          AND `status` = 1
          AND alarm_date = curdate();
    </select>

</mapper>