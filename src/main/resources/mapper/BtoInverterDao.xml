<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.dao.BtoInverterDao">

    <resultMap type="com.bto.entity.BtoInverterEntity" id="btoInverterMap">
        <result property="id" column="id"/>
        <result property="inverterSn" column="inverter_sn"/>
        <result property="power" column="power"/>
        <result property="todayElectricity" column="today_electricity"/>
        <result property="monthElectricity" column="month_electricity"/>
        <result property="yearElectricity" column="year_electricity"/>
        <result property="totalElectricity" column="total_electricity"/>
        <result property="initTime" column="init_time"/>
        <result property="ipv1" column="ipv1"/>
        <result property="ipv2" column="ipv2"/>
        <result property="ipv3" column="ipv3"/>
        <result property="ipv4" column="ipv4"/>
        <result property="ipv5" column="ipv5"/>
        <result property="ipv6" column="ipv6"/>
        <result property="ipv7" column="ipv7"/>
        <result property="ipv8" column="ipv8"/>
        <result property="ipv9" column="ipv9"/>
        <result property="ipv10" column="ipv10"/>
        <result property="ipv11" column="ipv11"/>
        <result property="ipv12" column="ipv12"/>
        <result property="vpv1" column="vpv1"/>
        <result property="vpv2" column="vpv2"/>
        <result property="vpv3" column="vpv3"/>
        <result property="vpv4" column="vpv4"/>
        <result property="vpv5" column="vpv5"/>
        <result property="vpv6" column="vpv6"/>
        <result property="vpv7" column="vpv7"/>
        <result property="vpv8" column="vpv8"/>
        <result property="vpv9" column="vpv9"/>
        <result property="vpv10" column="vpv10"/>
        <result property="vpv11" column="vpv11"/>
        <result property="vpv12" column="vpv12"/>
        <result property="iac1" column="iac1"/>
        <result property="iac2" column="iac2"/>
        <result property="iac3" column="iac3"/>
        <result property="vac1" column="vac1"/>
        <result property="vac2" column="vac2"/>
        <result property="vac3" column="vac3"/>
        <result property="temp" column="temp"/>
        <result property="fac1" column="fac1"/>
        <result property="fac2" column="fac2"/>
        <result property="fac3" column="fac3"/>
        <result property="pv1Power" column="pv1_power"/>
        <result property="pv2Power" column="pv2_power"/>
        <result property="pv3Power" column="pv3_power"/>
        <result property="pv4Power" column="pv4_power"/>
        <result property="pv5Power" column="pv5_power"/>
        <result property="pv6Power" column="pv6_power"/>
        <result property="pv7Power" column="pv7_power"/>
        <result property="pv8Power" column="pv8_power"/>
        <result property="pv9Power" column="pv9_power"/>
        <result property="pv10Power" column="pv10_power"/>
        <result property="pv11Power" column="pv11_power"/>
        <result property="pv12Power" column="pv12_power"/>
        <result property="state" column="state"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <insert id="insertInverter" parameterType="com.bto.entity.BtoInverterEntity">
        insert into ${tableName} (inverter_sn, power, today_electricity, month_electricity,
                                  year_electricity, total_electricity, init_time, ipv1, ipv2,
                                  ipv3, ipv4, ipv5, ipv6, ipv7, ipv8, ipv9, ipv10, ipv11,
                                  ipv12, vpv1, vpv2, vpv3, vpv4, vpv5, vpv6, vpv7, vpv8,
                                  vpv9, vpv10, vpv11, vpv12, iac1, iac2, iac3, vac1, vac2,
                                  vac3, temp, fac1, fac2, fac3, pv1_power, pv2_power, pv3_power,
                                  pv4_power, pv5_power, pv6_power, pv7_power, pv8_power, pv9_power,
                                  pv10_power, pv11_power, pv12_power, state, update_time)
        values (#{inverter.inverterSn}, #{inverter.power}, #{inverter.todayElectricity}, #{inverter.monthElectricity},
                #{inverter.yearElectricity}, #{inverter.totalElectricity}, #{inverter.initTime}, #{inverter.ipv1},
                #{inverter.ipv2},
                #{inverter.ipv3}, #{inverter.ipv4}, #{inverter.ipv5}, #{inverter.ipv6}, #{inverter.ipv7},
                #{inverter.ipv8}, #{inverter.ipv9},
                #{inverter.ipv10}, #{inverter.ipv11}, #{inverter.ipv12}, #{inverter.vpv1}, #{inverter.vpv2},
                #{inverter.vpv3}, #{inverter.vpv4},
                #{inverter.vpv5}, #{inverter.vpv6}, #{inverter.vpv7}, #{inverter.vpv8}, #{inverter.vpv9},
                #{inverter.vpv10}, #{inverter.vpv11},
                #{inverter.vpv12}, #{inverter.iac1}, #{inverter.iac2}, #{inverter.iac3}, #{inverter.vac1},
                #{inverter.vac2},
                #{inverter.vac3}, #{inverter.temp}, #{inverter.fac1}, #{inverter.fac2}, #{inverter.fac3},
                #{inverter.pv1Power}, #{inverter.pv2Power},
                #{inverter.pv3Power}, #{inverter.pv4Power}, #{inverter.pv5Power}, #{inverter.pv6Power},
                #{inverter.pv7Power}, #{inverter.pv8Power},
                #{inverter.pv9Power}, #{inverter.pv10Power}, #{inverter.pv11Power}, #{inverter.pv12Power},
                #{inverter.state},
                #{inverter.updateTime})</insert>

    <select id="getTenMinutesList" resultType="com.bto.entity.BtoInverterEntity">
        select id,
               inverter_sn,
               power,
               today_electricity,
               month_electricity,
               year_electricity,
               total_electricity,
               init_time,
               ipv1,
               ipv2,
               ipv3,
               ipv4,
               ipv5,
               ipv6,
               ipv7,
               ipv8,
               ipv9,
               ipv10,
               ipv11,
               ipv12,
               vpv1,
               vpv2,
               vpv3,
               vpv4,
               vpv5,
               vpv6,
               vpv7,
               vpv8,
               vpv9,
               vpv10,
               vpv11,
               vpv12,
               iac1,
               iac2,
               iac3,
               vac1,
               vac2,
               vac3,
               temp,
               fac1,
               fac2,
               fac3,
               pv1_power,
               pv2_power,
               pv3_power,
               pv4_power,
               pv5_power,
               pv6_power,
               pv7_power,
               pv8_power,
               pv9_power,
               pv10_power,
               pv11_power,
               pv12_power,
               state,
               update_time
        from ${tableName}
        where init_time &gt; #{tenMinutesAgo}
          and init_time &lt;= #{dateEnd}
    </select>
    <select id="getListByInverterSnAndTime" resultType="com.bto.entity.BtoInverterEntity">
        select id,
               inverter_sn,
               power,
               today_electricity,
               month_electricity,
               year_electricity,
               total_electricity,
               init_time,
               ipv1,
               ipv2,
               ipv3,
               ipv4,
               ipv5,
               ipv6,
               ipv7,
               ipv8,
               ipv9,
               ipv10,
               ipv11,
               ipv12,
               vpv1,
               vpv2,
               vpv3,
               vpv4,
               vpv5,
               vpv6,
               vpv7,
               vpv8,
               vpv9,
               vpv10,
               vpv11,
               vpv12,
               iac1,
               iac2,
               iac3,
               vac1,
               vac2,
               vac3,
               temp,
               fac1,
               fac2,
               fac3,
               pv1_power,
               pv2_power,
               pv3_power,
               pv4_power,
               pv5_power,
               pv6_power,
               pv7_power,
               pv8_power,
               pv9_power,
               pv10_power,
               pv11_power,
               pv12_power,
               state,
               update_time
        from ${tableName}
        where inverter_sn = #{inverterSn}
          and init_time = #{initTime}
    </select>
    <select id="getCloudInverterListByStatus" resultType="com.bto.entity.BtoInverterEntity">

    </select>

</mapper>