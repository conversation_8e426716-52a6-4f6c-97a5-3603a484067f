<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.dao.BtoInverterLatestDao">

    <resultMap type="com.bto.entity.BtoInverterLatestEntity" id="btoInverterLatestMap">
        <result property="plantUid" column="plant_uid"/>
        <result property="inverterSn" column="inverter_sn"/>
        <result property="inverterStatus" column="inverter_status"/>
        <result property="power" column="power"/>
        <result property="todayElectricity" column="today_electricity"/>
        <result property="monthElectricity" column="month_electricity"/>
        <result property="yearElectricity" column="year_electricity"/>
        <result property="totalElectricity" column="total_electricity"/>
        <result property="signalStrength" column="signal_strength"/>
        <result property="projectSpecial" column="project_special"/>
        <result property="createTime" column="create_time"/>
        <result property="state" column="state"/>
        <result property="updateTime" column="update_time"/>
        <result property="isElectricityMeter" column="is_electricity_meter"/>
        <result property="deviceImei" column="device_imei"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="res1" column="res1"/>
        <result property="res2" column="res2"/>
        <result property="res3" column="res3"/>
    </resultMap>

</mapper>