<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.dao.BtoSensorDataDao">

    <resultMap type="com.bto.entity.BtoSensorDataEntity" id="btoSensorDataMap">
        <result property="plantUid" column="plant_uid"/>
        <result property="sensorId" column="sensor_id"/>
        <result property="smokeConcentr" column="smoke_concentr"/>
        <result property="temp" column="temp"/>
        <result property="humidity" column="humidity"/>
        <result property="alarmStatus" column="alarm_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="getSensorDataList" resultType="com.bto.entity.BtoSensorDataEntity">
        select plant_uid,
               sensor_id,
               smoke_concentr,
               temp,
               humidity,
               alarm_status,
               create_time,
               update_time
        from sensor_data
        where create_time &gt;= #{time}
    </select>
</mapper>